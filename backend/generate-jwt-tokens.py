#!/usr/bin/env python3
"""
Generate proper JWT tokens for Supabase with the correct secret
"""

import jwt
import json
from datetime import datetime, timedelta

# JWT Secret from .env file
JWT_SECRET = "your-super-secret-jwt-token-with-at-least-32-characters-long"

# Token expiration (far in the future for development)
EXPIRY = datetime.utcnow() + timedelta(days=365 * 10)  # 10 years
EXPIRY_TIMESTAMP = int(EXPIRY.timestamp())

# Anon token payload
anon_payload = {
    "iss": "supabase-demo",
    "role": "anon",
    "exp": EXPIRY_TIMESTAMP
}

# Service role token payload
service_payload = {
    "iss": "supabase-demo", 
    "role": "service_role",
    "exp": EXPIRY_TIMESTAMP
}

# Generate tokens
anon_token = jwt.encode(anon_payload, JWT_SECRET, algorithm="HS256")
service_token = jwt.encode(service_payload, JWT_SECRET, algorithm="HS256")

print("Generated JWT Tokens:")
print("====================")
print()
print(f"JWT_SECRET={JWT_SECRET}")
print(f"ANON_KEY={anon_token}")
print(f"SERVICE_ROLE_KEY={service_token}")
print()
print("Copy these values to your .env file to fix authentication issues.")
print()

# Verify tokens work
try:
    decoded_anon = jwt.decode(anon_token, JWT_SECRET, algorithms=["HS256"])
    decoded_service = jwt.decode(service_token, JWT_SECRET, algorithms=["HS256"])
    print("✅ Token verification successful!")
    print(f"   Anon role: {decoded_anon['role']}")
    print(f"   Service role: {decoded_service['role']}")
    print(f"   Expiry: {datetime.fromtimestamp(decoded_anon['exp'])}")
except Exception as e:
    print(f"❌ Token verification failed: {e}")
