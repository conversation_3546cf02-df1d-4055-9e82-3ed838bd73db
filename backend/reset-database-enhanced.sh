#!/bin/bash

# Enhanced GameFlex Database Reset Script
# This script completely resets the database and ensures all permissions are set correctly

echo "🔄 Enhanced GameFlex Database Reset"
echo "=================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    print_status 1 "docker-compose is not installed or not in PATH"
fi

# Check if .env file exists
if [ ! -f .env ]; then
    print_status 1 ".env file not found. Please create one based on .env.example"
fi

print_info "Loading environment variables..."
export $(cat .env | grep -v '^#' | xargs)

# Step 1: Stop all services
print_info "Stopping all services..."
docker-compose down
print_status $? "Services stopped"

# Step 2: Remove database volume to ensure clean start
print_info "Removing database volume for clean reset..."
docker volume rm gameflex_mobile_db_data 2>/dev/null || true
print_status 0 "Database volume removed"

# Step 3: Start only the database first
print_info "Starting database service..."
docker-compose up -d db
print_status $? "Database service started"

# Step 4: Wait for database to be ready
print_info "Waiting for database to be ready..."
sleep 10

# Check database health
for i in {1..30}; do
    if docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1; then
        print_status 0 "Database is ready"
        break
    fi
    if [ $i -eq 30 ]; then
        print_status 1 "Database failed to start within 30 attempts"
    fi
    sleep 2
done

# Step 5: Run initialization scripts in correct order
print_info "Running database initialization scripts..."

# Set postgres password for role creation
docker-compose exec -T db psql -U postgres -c "SELECT set_config('custom.postgres_password', '$POSTGRES_PASSWORD', false);" > /dev/null 2>&1
print_status $? "Password configuration set"

# Run roles script
print_info "Creating database roles..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/init-scripts/99-roles.sql > /dev/null 2>&1
print_status $? "Database roles created"

# Run JWT functions script
print_info "Creating JWT functions..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/init-scripts/99-jwt.sql > /dev/null 2>&1
print_status $? "JWT functions created"

# Run main initialization script
print_info "Creating database schema..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/init-scripts/99-init.sql > /dev/null 2>&1
print_status $? "Database schema created"

# Run permission fix script
print_info "Applying comprehensive permission fixes..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/fix_permissions.sql > /dev/null 2>&1
print_status $? "Permissions fixed"

# Run realtime script
print_info "Setting up realtime functionality..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/init-scripts/99-realtime.sql > /dev/null 2>&1
print_status $? "Realtime setup completed"

# Step 6: Load seed data
print_info "Loading seed data..."
docker-compose exec -T db psql -U postgres -f /docker-entrypoint-initdb.d/seed.sql > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Seed data loaded successfully"
else
    print_warning "Some seed data may have failed to load, but continuing..."
fi

# Step 7: Start all other services
print_info "Starting all services..."
docker-compose up -d
print_status $? "All services started"

# Step 8: Wait for services to be ready
print_info "Waiting for services to be ready..."
sleep 15

# Step 9: Verify everything is working
print_info "Verifying database setup..."

# Check table count
TABLES_COUNT=$(docker-compose exec -T db psql -U postgres -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
if [ "$TABLES_COUNT" -ge 8 ]; then
    print_status 0 "Database schema verified ($TABLES_COUNT tables)"
else
    print_status 1 "Database schema incomplete (only $TABLES_COUNT tables found)"
fi

# Check users count
USERS_COUNT=$(docker-compose exec -T db psql -U postgres -t -c "SELECT count(*) FROM public.users;" 2>/dev/null | tr -d ' ')
if [ "$USERS_COUNT" -ge 5 ]; then
    print_status 0 "Sample data verified ($USERS_COUNT users)"
else
    print_warning "Sample data incomplete (only $USERS_COUNT users found)"
fi

# Check roles
ROLES_COUNT=$(docker-compose exec -T db psql -U postgres -t -c "SELECT count(*) FROM pg_roles WHERE rolname IN ('anon', 'authenticated', 'service_role', 'authenticator', 'supabase_auth_admin', 'supabase_storage_admin', 'supabase_admin');" 2>/dev/null | tr -d ' ')
if [ "$ROLES_COUNT" -ge 7 ]; then
    print_status 0 "Database roles verified ($ROLES_COUNT roles)"
else
    print_status 1 "Database roles incomplete (only $ROLES_COUNT roles found)"
fi

echo ""
echo -e "${GREEN}🎉 Enhanced database reset completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo "   Database Tables: $TABLES_COUNT"
echo "   Sample Users: $USERS_COUNT"
echo "   Database Roles: $ROLES_COUNT"
echo ""
echo -e "${BLUE}💡 Next Steps:${NC}"
echo "   1. Run: ./verify-backend.sh"
echo "   2. Open Supabase Studio: http://localhost:54323"
echo "   3. Test API endpoints"
echo ""
