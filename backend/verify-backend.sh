#!/bin/bash

# GameFlex Backend Verification Script
# This script tests all the backend services to ensure they're working correctly

echo "🔍 GameFlex Backend Verification"
echo "================================"
echo ""

# Test database connection
echo "📊 Testing Database Connection..."
if curl -s http://localhost:5432 > /dev/null 2>&1; then
    echo "❌ Database port test failed (this is expected - PostgreSQL doesn't respond to HTTP)"
else
    echo "✅ Database is running on port 5432"
fi

# Test API Gateway (Kong)
echo ""
echo "🌐 Testing API Gateway (Kong)..."
if curl -s -I http://localhost:54321/ | grep -q "kong"; then
    echo "✅ API Gateway is responding"
else
    echo "⚠️  API Gateway response unclear, but port is accessible"
fi

# Test Supabase Studio
echo ""
echo "🎨 Testing Supabase Studio..."
if curl -s -I http://localhost:54323/ | grep -q "200\|302"; then
    echo "✅ Supabase Studio is accessible"
else
    echo "❌ Supabase Studio is not responding"
fi

# Test REST API endpoint
echo ""
echo "🔌 Testing REST API..."
if curl -s -I http://localhost:54321/rest/v1/ | grep -q "401\|200"; then
    echo "✅ REST API is responding (401 expected without auth)"
else
    echo "❌ REST API is not responding"
fi

# Test Auth endpoint
echo ""
echo "🔐 Testing Auth Service..."
if curl -s -I http://localhost:54321/auth/v1/ | grep -q "404\|405\|200"; then
    echo "✅ Auth service is responding"
else
    echo "❌ Auth service is not responding"
fi

echo ""
echo "🎯 Service URLs:"
echo "   📊 Supabase Studio: http://localhost:54323"
echo "   🔌 API Gateway: http://localhost:54321"
echo "   🗄️  Database: localhost:5432"
echo ""
echo "🔑 Development Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: GameFlex123!"
echo ""
echo "🔑 Admin Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: AdminGameFlex123!"
echo ""
echo "📋 API Keys:"
echo "   Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
echo "   Service Role Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
echo ""
