#!/bin/bash

# GameFlex Backend Comprehensive Verification Script
# This script tests all the backend services to ensure they're working correctly

echo "🔍 GameFlex Backend Comprehensive Verification"
echo "=============================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# API Keys
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test 1: Docker containers status
echo "🐳 Testing Docker Containers..."
docker-compose ps --format "table {{.Name}}\t{{.Status}}" | grep -E "(Up|healthy)" > /dev/null
print_status $? "Docker containers are running"

# Test 2: Database connection and health
echo ""
echo "📊 Testing Database..."
docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1
print_status $? "Database connection is healthy"

# Test database schema
TABLES_COUNT=$(docker-compose exec -T db psql -U postgres -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | tr -d ' ')
if [ "$TABLES_COUNT" -ge 8 ]; then
    print_status 0 "Database schema is properly initialized ($TABLES_COUNT tables)"
else
    print_status 1 "Database schema incomplete (only $TABLES_COUNT tables found)"
fi

# Test sample data
USERS_COUNT=$(docker-compose exec -T db psql -U postgres -t -c "SELECT count(*) FROM public.users;" 2>/dev/null | tr -d ' ')
if [ "$USERS_COUNT" -ge 5 ]; then
    print_status 0 "Sample data loaded ($USERS_COUNT users)"
else
    print_status 1 "Sample data missing (only $USERS_COUNT users found)"
fi

# Test 3: API Gateway (Kong)
echo ""
echo "🌐 Testing API Gateway (Kong)..."
curl -s -f http://localhost:54321/health > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "API Gateway is responding"
else
    curl -s -I http://localhost:54321/ | grep -q "kong\|200\|404" > /dev/null 2>&1
    print_status $? "API Gateway is accessible"
fi

# Test 4: REST API
echo ""
echo "🔌 Testing REST API..."
curl -s -I http://localhost:54321/rest/v1/ | grep -q "401\|200" > /dev/null 2>&1
print_status $? "REST API endpoint is responding"

# Test REST API with authentication
USERS_RESPONSE=$(curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" http://localhost:54321/rest/v1/users?select=id,username,email&limit=1 2>/dev/null)
if echo "$USERS_RESPONSE" | grep -q "id\|username" > /dev/null 2>&1; then
    print_status 0 "REST API authentication working"
    print_info "Sample user data retrieved successfully"
else
    print_status 1 "REST API authentication failed"
    print_warning "Response: $USERS_RESPONSE"
fi

# Test 5: Auth Service
echo ""
echo "🔐 Testing Auth Service..."
curl -s -I http://localhost:54321/auth/v1/health | grep -q "200\|404\|405" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Auth service is responding"
else
    print_status 1 "Auth service is not responding"
fi

# Test 6: Storage Service
echo ""
echo "💾 Testing Storage Service..."
curl -s -I http://localhost:54321/storage/v1/bucket | grep -q "401\|200\|404" > /dev/null 2>&1
print_status $? "Storage service is responding"

# Test 7: Realtime Service
echo ""
echo "⚡ Testing Realtime Service..."
curl -s -I http://localhost:54321/realtime/v1/ | grep -q "426\|200\|404" > /dev/null 2>&1
print_status $? "Realtime service is responding"

# Test 8: Supabase Studio
echo ""
echo "🎨 Testing Supabase Studio..."
curl -s -I http://localhost:54323/ | grep -q "200\|302" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Supabase Studio is accessible"
else
    print_status 1 "Supabase Studio is not responding"
fi

# Test 9: Edge Functions
echo ""
echo "⚙️  Testing Edge Functions..."
curl -s -I http://localhost:54321/functions/v1/ | grep -q "404\|401\|200" > /dev/null 2>&1
print_status $? "Edge Functions service is responding"

# Test 10: Database Performance
echo ""
echo "⚡ Testing Database Performance..."
QUERY_TIME=$(docker-compose exec -T db psql -U postgres -c "\timing on" -c "SELECT count(*) FROM public.users;" 2>&1 | grep "Time:" | awk '{print $2}' | head -1)
if [ ! -z "$QUERY_TIME" ]; then
    print_status 0 "Database queries executing (${QUERY_TIME})"
else
    print_status 0 "Database queries executing"
fi

# Summary
echo ""
echo "📋 Summary"
echo "=========="
echo ""

# Count services
TOTAL_SERVICES=10
RUNNING_SERVICES=$(docker-compose ps --format "{{.Status}}" | grep -c "Up")

echo -e "${BLUE}🎯 Service URLs:${NC}"
echo "   📊 Supabase Studio: http://localhost:54323"
echo "   🔌 API Gateway: http://localhost:54321"
echo "   🗄️  Database: localhost:5432"
echo "   📖 API Documentation: http://localhost:54321/rest/v1/"
echo ""

echo -e "${BLUE}🔑 Development Credentials:${NC}"
echo "   Email: <EMAIL>"
echo "   Password: GameFlex123!"
echo ""

echo -e "${BLUE}🔑 Admin Credentials:${NC}"
echo "   Email: <EMAIL>"
echo "   Password: AdminGameFlex123!"
echo ""

echo -e "${BLUE}📋 API Keys:${NC}"
echo "   Anon Key: $ANON_KEY"
echo "   Service Role Key: $SERVICE_KEY"
echo ""

echo -e "${BLUE}📊 Status Overview:${NC}"
echo "   Docker Services Running: $RUNNING_SERVICES"
echo "   Database Tables: $TABLES_COUNT"
echo "   Sample Users: $USERS_COUNT"
echo ""

# Final status
if [ $RUNNING_SERVICES -ge 8 ] && [ "$TABLES_COUNT" -ge 8 ] && [ "$USERS_COUNT" -ge 5 ]; then
    echo -e "${GREEN}🎉 Backend is fully operational!${NC}"
    echo -e "${GREEN}   All core services are running and data is properly loaded.${NC}"
    echo ""
    echo -e "${BLUE}💡 Next Steps:${NC}"
    echo "   1. Open Supabase Studio: http://localhost:54323"
    echo "   2. Test API endpoints with the provided keys"
    echo "   3. Start your frontend application"
else
    echo -e "${YELLOW}⚠️  Backend has some issues that need attention.${NC}"
    echo -e "${YELLOW}   Check the individual test results above for details.${NC}"
fi

echo ""
