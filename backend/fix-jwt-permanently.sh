#!/bin/bash

# Permanent JWT Fix Script for GameFlex Backend
# This script fixes JWT authentication issues permanently

echo "🔧 Fixing JWT Authentication Permanently"
echo "========================================"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# The standard Supabase demo JWT secret that matches the provided tokens
CORRECT_JWT_SECRET="your-super-secret-jwt-token-with-at-least-32-characters-long"

print_info "Updating JWT secret in .env file..."

# Update the JWT secret in .env file
if [ -f .env ]; then
    # Create backup
    cp .env .env.backup
    
    # Update JWT secret
    sed -i "s/^JWT_SECRET=.*/JWT_SECRET=$CORRECT_JWT_SECRET/" .env
    print_status $? "JWT secret updated in .env file"
else
    print_status 1 ".env file not found"
fi

print_info "Restarting services to apply JWT configuration..."

# Restart services that use JWT
docker-compose restart rest auth kong
print_status $? "Services restarted"

print_info "Waiting for services to be ready..."
sleep 10

# Test the API with the service role key
print_info "Testing API authentication..."
RESPONSE=$(curl -s -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU" "http://localhost:54321/rest/v1/users?select=username,email&limit=1")

if echo "$RESPONSE" | grep -q "username\|email"; then
    print_status 0 "API authentication is working!"
    echo -e "${GREEN}   Sample response: $RESPONSE${NC}"
elif echo "$RESPONSE" | grep -q "JWSError"; then
    print_status 1 "JWT signature still invalid - may need manual token regeneration"
else
    print_status 1 "Unexpected API response: $RESPONSE"
fi

print_info "Testing with anon key..."
ANON_RESPONSE=$(curl -s -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" "http://localhost:54321/rest/v1/channels?select=name&limit=1")

if echo "$ANON_RESPONSE" | grep -q "name" || echo "$ANON_RESPONSE" | grep -q "\[\]"; then
    print_status 0 "Anon key authentication is working!"
    echo -e "${GREEN}   Sample response: $ANON_RESPONSE${NC}"
elif echo "$ANON_RESPONSE" | grep -q "JWSError"; then
    print_status 1 "Anon JWT signature still invalid"
else
    print_info "Anon response (may be empty due to RLS): $ANON_RESPONSE"
fi

echo ""
echo -e "${GREEN}🎉 JWT Authentication Fix Completed!${NC}"
echo ""
echo -e "${BLUE}📋 Current Configuration:${NC}"
echo "   JWT Secret: $CORRECT_JWT_SECRET"
echo "   Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
echo "   Service Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
echo ""
echo -e "${BLUE}💡 Next Steps:${NC}"
echo "   1. Run: ./verify-backend.sh"
echo "   2. Test API endpoints with the keys above"
echo "   3. The backend should now work reliably after restarts"
echo ""
